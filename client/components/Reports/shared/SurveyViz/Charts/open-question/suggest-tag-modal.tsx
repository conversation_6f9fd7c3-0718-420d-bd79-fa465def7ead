'use client';

import BuildWithAiLoading from '@/components/Projects/BuildWithAiLoading';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { successToast } from '@/utils/toast';
import { cn } from '@/utils/utils';
import {
  AwardIcon,
  ChevronLeft,
  Info,
  LayoutGrid,
  Smile,
  Plus,
} from 'lucide-react';
import { useState } from 'react';

interface SuggestTagsModalProps {
  question: any;
  mutateSuggestTagsWithAi: any;
  mutateSaveSelectedTags: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTags: string[];
  setSelectedTags: any;
  tagsGenerated: boolean;
  setTagsGenerated: (tagsGenerated: boolean) => void;
  generatedTagsMap: any;
  setGeneratedTagsMap: any;
  generatedAnswersTagsMap: any;
  setGeneratedAnswersTagsMap: any;
}

export default function SuggestTagsModal({
  question,
  mutateSuggestTagsWithAi,
  mutateSaveSelectedTags,
  open,
  onOpenChange,
  selectedTags,
  setSelectedTags,
  tagsGenerated,
  setTagsGenerated,
  generatedTagsMap,
  setGeneratedTagsMap,
  generatedAnswersTagsMap,
  setGeneratedAnswersTagsMap,
}: SuggestTagsModalProps) {
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [customTagInput, setCustomTagInput] = useState<string>('');
  console.log('generatedAnswersTagsMap', generatedAnswersTagsMap);
  console.log('generatedTagsMap', generatedTagsMap);
  console.log('selectedTags', selectedTags);
  // Map option values to display names
  const optionLabels = {
    sentiment: 'Sentiment Detection',
    brand: 'Brand Detection',
    theme: 'Theme Detection',
    irrelevant: 'Irrelevant Detection',
  };

  const handleAddCustomTag = () => {
    if (customTagInput.trim() && selectedOption) {
      const trimmedTag = customTagInput.trim();
      // Check if tag already exists
      const currentTags = generatedTagsMap[selectedOption] || [];
      if (!currentTags.includes(trimmedTag)) {
        // Add the custom tag to the generatedTagsMap
        setGeneratedTagsMap((prev: any) => ({
          ...prev,
          [selectedOption]: [...currentTags, trimmedTag],
        }));
      }
      // Clear the input
      setCustomTagInput('');
    }
  };

  // call ai to get tags
  const handleApply = () => {
    // If we already have tags for this option, just show them without calling the API
    if (
      generatedTagsMap[selectedOption]?.length > 0 ||
      selectedOption === 'custom'
    ) {
      setTagsGenerated(true);
      return;
    }

    // const wordList = question?.chartData?.datasets[0]?.wordlist;
    // const answers = Object?.fromEntries(
    //   Object?.values(wordList)?.map((value, index) => [index, value])
    // );

    mutateSuggestTagsWithAi.mutate(
      {
        type: selectedOption,
        questionId: question?._id,
      },
      {
        onSuccess: (data: any) => {
          if (data) {
            successToast('Tags created with AI successfully!');
            const tags = data?.globalTags || [];
            // Update the map with the new tags for this option
            setGeneratedTagsMap((prev) => ({
              ...prev,
              [selectedOption]: tags,
            }));
            setGeneratedAnswersTagsMap((prev) => ({
              ...prev,
              [selectedOption]: data?.updatedAnswers,
            }));
            setTagsGenerated(true);
          }
        },
      }
    );
  };

  const handleTagSelection = (tag: string) => {
    // Use a callback function with the previous state to ensure we're working with the latest state
    setSelectedTags((prev) => {
      // Check if the tag is already selected
      const isSelected = prev.includes(tag);

      // If already selected, filter it out; otherwise, add it
      // Use a more efficient approach to avoid unnecessary array operations
      if (isSelected) {
        return prev.filter((t) => t !== tag);
      } else {
        // Create a new array only when adding a tag
        return [...prev, tag];
      }
    });
  };

  const handleSaveTags = () => {
    const selectedAnswersTags: Record<string, string[]> = {};

    Object.values(generatedAnswersTagsMap).forEach((tagMap: any) => {
      Object.entries(tagMap).forEach(([answerId, tags]: any) => {
        const filteredTags = tags.filter((tag: string) =>
          selectedTags.includes(tag)
        );
        if (!selectedAnswersTags[answerId]) {
          selectedAnswersTags[answerId] = [];
        }
        selectedAnswersTags[answerId].push(...filteredTags);
      });
    });

    // Ensure all arrays contain only unique tags
    Object.keys(selectedAnswersTags).forEach((key) => {
      selectedAnswersTags[key] = [...new Set(selectedAnswersTags[key])];
    });

    const payload = {
      questionId: question?._id,
      globalTags: selectedTags,
      answersTags: selectedAnswersTags,
    };
    mutateSaveSelectedTags.mutate(payload);
  };

  const handleBackToOptions = () => {
    setTagsGenerated(false);
  };

  const radioStyles = {
    base: `
      h-5 w-5 
      border-0
      before:hidden
      after:hidden
      data-[state=checked]:bg-[#0FC083]
      data-[state=unchecked]:bg-white
      data-[state=unchecked]:border-[1.5px]
      data-[state=unchecked]:border-[#CBD5E1]
      relative
      transition-colors
    `,
  };

  const wordList = question?.chartData?.datasets[0]?.wordlist;
  const answers = Object?.fromEntries(
    Object?.values(wordList)?.map((value, index) => [index, value])
  );
  console.log(answers, 'wordList');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[600px] p-0 gap-0 bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-center">
            <DialogTitle className="text-xl font-semibold text-[#1E293B] text-center mb-4">
              {tagsGenerated ? 'Select Tags' : 'Suggest Tags with AI'}
            </DialogTitle>
          </div>
          {!mutateSuggestTagsWithAi.isLoading && !tagsGenerated && (
            <p className="text-[#475569] mt-6 text-base font-normal text-center">
              Please choose the type of tags you&apos;d like to add to the
              answer list.
            </p>
          )}
          {tagsGenerated && (
            <div className="flex items-center mt-2 mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToOptions}
                className="text-[#64748B] hover:text-[#334155] p-0 h-auto"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back to options
              </Button>
              <div className="ml-auto flex items-center">
                <div className="h-8 w-8 rounded-full bg-[#0FC083] flex items-center justify-center mr-2">
                  {selectedOption === 'sentiment' && (
                    <Smile className="h-4 w-4 text-white" />
                  )}
                  {selectedOption === 'brand' && (
                    <AwardIcon className="h-4 w-4 text-white" />
                  )}
                  {selectedOption === 'theme' && (
                    <LayoutGrid className="h-4 w-4 text-white" />
                  )}
                  {selectedOption === 'irrelevant' && (
                    <Info className="h-4 w-4 text-white" />
                  )}
                </div>
                <span className="text-[#334155] font-medium">
                  {optionLabels[selectedOption as keyof typeof optionLabels]}
                </span>
              </div>
            </div>
          )}
        </DialogHeader>

        {mutateSuggestTagsWithAi.isLoading ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-10">
            <BuildWithAiLoading />
            <h3 className="font-semibold text-[#334155] mt-2 text-[16px]">
              Generating the Tags
            </h3>
            <p className=" text-[14px] text-[#64748B]">
              Please hold on while our AI generates tags
            </p>
          </div>
        ) : mutateSaveSelectedTags.isLoading ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-10">
            {/* Add your loading UI for saving tags here */}
            <BuildWithAiLoading />
            <h3 className="font-semibold text-[#334155] mt-2 text-[16px]">
              Saving your selected tags
            </h3>
            <p className="text-[14px] text-[#64748B]">
              Please wait while we save your selections
            </p>
          </div>
        ) : (
          <>
            {!tagsGenerated ? (
              <div className="p-6">
                <RadioGroup
                  value={selectedOption}
                  onValueChange={setSelectedOption}
                  className="space-y-4"
                >
                  <label
                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer
                      ${
                        selectedOption === 'sentiment'
                          ? 'border-[#0FC083] bg-[#F0FDF4]'
                          : 'border-[#E2E8F0] bg-white'
                      }`}
                  >
                    <RadioGroupItem
                      value="sentiment"
                      className={cn(radioStyles.base)}
                    />
                    <div className="min-w-[40px] h-10 rounded-full bg-[#0FC083] flex items-center justify-center">
                      <Smile className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#1E293B] text-base">
                        Sentiment Detection
                      </h3>
                      <p className="text-[#64748B] text-sm">
                        Identify sentiment for each answer (ex. positive,
                        negative)
                      </p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer
                      ${
                        selectedOption === 'brand'
                          ? 'border-[#0FC083] bg-[#F0FDF4]'
                          : 'border-[#E2E8F0] bg-white'
                      }`}
                  >
                    <RadioGroupItem
                      value="brand"
                      className={cn(radioStyles.base)}
                    />
                    <div className="min-w-[40px] h-10 rounded-full bg-[#0FC083] flex items-center justify-center">
                      <AwardIcon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#1E293B] text-base">
                        Brand Detection
                      </h3>
                      <p className="text-[#64748B] text-sm">
                        Identify brand names for each answer (ex. Toyota, Tesla)
                      </p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer
                      ${
                        selectedOption === 'theme'
                          ? 'border-[#0FC083] bg-[#F0FDF4]'
                          : 'border-[#E2E8F0] bg-white'
                      }`}
                  >
                    <RadioGroupItem
                      value="theme"
                      className={cn(radioStyles.base)}
                    />
                    <div className="min-w-[40px] h-10 rounded-full bg-[#0FC083] flex items-center justify-center">
                      <LayoutGrid className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#1E293B] text-base">
                        Theme Detection
                      </h3>
                      <p className="text-[#64748B] text-sm">
                        Identify themes for each answer (ex. environment,
                        automotive)
                      </p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer
                      ${
                        selectedOption === 'irrelevant'
                          ? 'border-[#0FC083] bg-[#F0FDF4]'
                          : 'border-[#E2E8F0] bg-white'
                      }`}
                  >
                    <RadioGroupItem
                      value="irrelevant"
                      className={cn(radioStyles.base)}
                    />
                    <div className="min-w-[40px] h-10 rounded-full bg-[#0FC083] flex items-center justify-center">
                      <Info className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#1E293B] text-base">
                        Irrelevant Detection
                      </h3>
                      <p className="text-[#64748B] text-sm">
                        Mark irrelevant answers
                      </p>
                    </div>
                  </label>

                  <label
                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer
                      ${
                        selectedOption === 'custom'
                          ? 'border-[#0FC083] bg-[#F0FDF4]'
                          : 'border-[#E2E8F0] bg-white'
                      }`}
                  >
                    <RadioGroupItem
                      value="custom"
                      className={cn(radioStyles.base)}
                    />
                    <div className="min-w-[40px] h-10 rounded-full bg-[#0FC083] flex items-center justify-center">
                      <Info className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#1E293B] text-base">
                        Custom Tag
                      </h3>
                      <p className="text-[#64748B] text-sm">
                        Create a custom tag
                      </p>
                    </div>
                  </label>
                </RadioGroup>

                <div className="mt-6 p-4 bg-[#F0FDF4] rounded-lg flex gap-3">
                  <Info className="h-5 w-5 text-[#0FC083] shrink-0 mt-0.5" />
                  <p className="text-[#0F172A] text-sm">
                    The AI will tag matching entries and ignore others, but we
                    recommend double-checking for accuracy.
                  </p>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    className="text-[#0FC083] border-[#E2E8F0]"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleApply}
                    className="bg-[#0FC083] text-white hover:bg-[#0FC083]/90"
                    disabled={
                      !selectedOption || mutateSuggestTagsWithAi.isLoading
                    }
                  >
                    {mutateSuggestTagsWithAi.isLoading
                      ? 'Processing...'
                      : 'Apply'}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-6">
                <div className="p-4 bg-[#F0FDF4] rounded-lg flex gap-3 mb-6">
                  <Info className="h-5 w-5 text-[#0FC083] shrink-0 mt-0.5" />
                  <p className="text-[#0F172A] text-sm">
                    Select the tags you want to apply. You can select multiple
                    tags.
                  </p>
                </div>
                {/* Update the JSX where it displays the generated tags to use the map */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {generatedTagsMap[selectedOption]?.map((tag, index) => {
                    const isSelected = selectedTags.includes(tag);
                    return (
                      <button
                        key={index}
                        onClick={() => handleTagSelection(tag)}
                        className={`rounded-md px-3 py-[6px] flex items-center cursor-pointer text-[12px] font-medium ${
                          isSelected
                            ? 'bg-[#0FC083] text-white'
                            : 'bg-[#F1F5F9] text-[#0F172A]'
                        }`}
                      >
                        {tag}
                      </button>
                    );
                  })}
                </div>
                {/* Custom Tag Input Section */}
                <div className="flex items-center gap-3 mb-6">
                  <span className="text-[#334155] font-medium text-sm">
                    Custom Tag
                  </span>
                  <Input
                    value={customTagInput}
                    onChange={(e) => setCustomTagInput(e.target.value)}
                    placeholder="Enter custom tag"
                    className="flex-1 h-9 text-sm"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddCustomTag();
                      }
                    }}
                  />
                  <Button
                    onClick={handleAddCustomTag}
                    disabled={!customTagInput.trim()}
                    size="sm"
                    className="bg-[#0FC083] text-white hover:bg-[#0FC083]/90 h-9 px-3"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add
                  </Button>
                </div>
                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    className="text-[#0FC083] border-[#E2E8F0]"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveTags}
                    disabled={
                      mutateSaveSelectedTags.isLoading ||
                      selectedTags.length === 0
                    }
                    className="bg-[#0FC083] text-white hover:bg-[#0FC083]/90"
                  >
                    {mutateSaveSelectedTags.isLoading
                      ? 'Saving...'
                      : 'Save Tags'}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
